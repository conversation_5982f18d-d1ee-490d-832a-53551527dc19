<?php
/**
 * Authentication System for Video Booking
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Video Booking Authentication Class
 */
class VideoBookingAuth {
    
    /**
     * Initialize authentication system
     */
    public static function init() {
        // Add rewrite rules for auth pages
        add_action('init', array(__CLASS__, 'add_auth_rewrite_rules'));
        
        // Handle auth page templates
        add_action('template_redirect', array(__CLASS__, 'handle_auth_templates'));
        
        // Add AJAX handlers
        add_action('wp_ajax_nopriv_video_login', array(__CLASS__, 'handle_login'));
        add_action('wp_ajax_nopriv_video_register', array(__CLASS__, 'handle_register'));
        add_action('wp_ajax_video_logout', array(__CLASS__, 'handle_logout'));
        add_action('wp_ajax_nopriv_video_logout', array(__CLASS__, 'handle_logout'));
        
        // Enqueue auth scripts
        add_action('wp_enqueue_scripts', array(__CLASS__, 'enqueue_auth_scripts'));
        
        // Add login/logout links to menu
        add_filter('wp_nav_menu_items', array(__CLASS__, 'add_auth_menu_items'), 10, 2);
    }
    
    /**
     * Add rewrite rules for authentication pages
     */
    public static function add_auth_rewrite_rules() {
        // Login page
        add_rewrite_rule(
            '^login/?$',
            'index.php?video_auth=login',
            'top'
        );
        
        // Register page
        add_rewrite_rule(
            '^register/?$',
            'index.php?video_auth=register',
            'top'
        );
        
        // My account page
        add_rewrite_rule(
            '^my-account/?$',
            'index.php?video_auth=account',
            'top'
        );
        
        // Add query var
        add_rewrite_tag('%video_auth%', '([^&]+)');
        
        // Flush rewrite rules if needed
        if (get_option('video_auth_rewrite_flushed') !== VIDEO_BOOKING_VERSION) {
            flush_rewrite_rules();
            update_option('video_auth_rewrite_flushed', VIDEO_BOOKING_VERSION);
        }
    }
    
    /**
     * Handle authentication templates
     */
    public static function handle_auth_templates() {
        $auth_page = get_query_var('video_auth');
        
        if (!$auth_page) {
            return;
        }
        
        $templates = array(
            'login' => 'auth/login.php',
            'register' => 'auth/register.php',
            'account' => 'auth/account.php'
        );
        
        if (!isset($templates[$auth_page])) {
            return;
        }
        
        // Redirect logged-in users away from login/register pages
        if (in_array($auth_page, array('login', 'register')) && is_user_logged_in()) {
            wp_redirect(home_url('/my-account'));
            exit;
        }
        
        // Redirect non-logged-in users to login from account page
        if ($auth_page === 'account' && !is_user_logged_in()) {
            wp_redirect(home_url('/login'));
            exit;
        }
        
        $template_file = VIDEO_BOOKING_PATH . 'templates/' . $templates[$auth_page];
        
        if (file_exists($template_file)) {
            include $template_file;
            exit;
        }
    }
    
    /**
     * Handle login AJAX request
     */
    public static function handle_login() {
        check_ajax_referer('video_auth_nonce', 'nonce');
        
        $username = sanitize_user($_POST['username']);
        $password = $_POST['password'];
        $remember = isset($_POST['remember']) ? true : false;
        
        if (empty($username) || empty($password)) {
            wp_send_json_error('Please fill in all fields');
        }
        
        $credentials = array(
            'user_login' => $username,
            'user_password' => $password,
            'remember' => $remember
        );
        
        $user = wp_signon($credentials, false);
        
        if (is_wp_error($user)) {
            wp_send_json_error($user->get_error_message());
        }
        
        wp_send_json_success(array(
            'message' => 'Login successful!',
            'redirect_url' => home_url('/my-account/my-recordings')
        ));
    }
    
    /**
     * Handle registration AJAX request
     */
    public static function handle_register() {
        check_ajax_referer('video_auth_nonce', 'nonce');
        
        $username = sanitize_user($_POST['username']);
        $email = sanitize_email($_POST['email']);
        $password = $_POST['password'];
        $confirm_password = $_POST['confirm_password'];
        $first_name = sanitize_text_field($_POST['first_name']);
        $last_name = sanitize_text_field($_POST['last_name']);
        
        // Validation
        if (empty($username) || empty($email) || empty($password)) {
            wp_send_json_error('Please fill in all required fields');
        }
        
        if (!is_email($email)) {
            wp_send_json_error('Please enter a valid email address');
        }
        
        if ($password !== $confirm_password) {
            wp_send_json_error('Passwords do not match');
        }
        
        if (strlen($password) < 6) {
            wp_send_json_error('Password must be at least 6 characters long');
        }
        
        if (username_exists($username)) {
            wp_send_json_error('Username already exists');
        }
        
        if (email_exists($email)) {
            wp_send_json_error('Email address is already registered');
        }
        
        // Create user
        $user_id = wp_create_user($username, $password, $email);
        
        if (is_wp_error($user_id)) {
            wp_send_json_error($user_id->get_error_message());
        }
        
        // Update user meta
        wp_update_user(array(
            'ID' => $user_id,
            'first_name' => $first_name,
            'last_name' => $last_name,
            'display_name' => trim($first_name . ' ' . $last_name)
        ));
        
        // Auto-login the user
        wp_set_current_user($user_id);
        wp_set_auth_cookie($user_id);
        
        // Send welcome email
        self::send_welcome_email($user_id);
        
        wp_send_json_success(array(
            'message' => 'Registration successful! Welcome to our community.',
            'redirect_url' => home_url('/my-account/my-recordings')
        ));
    }
    
    /**
     * Handle logout AJAX request
     */
    public static function handle_logout() {
        check_ajax_referer('video_auth_nonce', 'nonce');
        
        wp_logout();
        
        wp_send_json_success(array(
            'message' => 'Logged out successfully',
            'redirect_url' => home_url()
        ));
    }
    
    /**
     * Send welcome email to new users
     */
    private static function send_welcome_email($user_id) {
        $user = get_user_by('id', $user_id);
        if (!$user) {
            return false;
        }
        
        $subject = 'Welcome to ' . get_bloginfo('name') . '!';
        
        $message = "
        <h2>Welcome to " . get_bloginfo('name') . "!</h2>
        
        <p>Hi {$user->display_name},</p>
        
        <p>Thank you for creating an account with us! You can now:</p>
        
        <ul>
            <li>Browse and purchase workshop recordings</li>
            <li>Access your purchased videos anytime</li>
            <li>Manage your account settings</li>
        </ul>
        
        <p><a href='" . home_url('/workshop-recordings') . "' style='background: #2c5aa0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Browse Workshop Recordings</a></p>
        
        <p>If you have any questions, feel free to contact us at " . get_option('admin_email') . "</p>
        
        <p>Best regards,<br>The " . get_bloginfo('name') . " Team</p>
        ";
        
        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
        );
        
        return wp_mail($user->user_email, $subject, $message, $headers);
    }
    
    /**
     * Enqueue authentication scripts
     */
    public static function enqueue_auth_scripts() {
        $auth_page = get_query_var('video_auth');
        
        if ($auth_page || is_page() || is_front_page()) {
            wp_enqueue_script(
                'video-auth',
                VIDEO_BOOKING_URL . 'assets/js/video-auth.js',
                array('jquery'),
                VIDEO_BOOKING_VERSION,
                true
            );
            
            wp_localize_script('video-auth', 'videoAuth', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('video_auth_nonce'),
                'login_url' => home_url('/login'),
                'register_url' => home_url('/register'),
                'account_url' => home_url('/my-account'),
                'is_logged_in' => is_user_logged_in(),
                'current_user' => is_user_logged_in() ? wp_get_current_user()->display_name : ''
            ));
        }
    }
    
    /**
     * Add login/logout links to navigation menu
     */
    public static function add_auth_menu_items($items, $args) {
        // Only add to primary menu
        if ($args->theme_location !== 'primary') {
            return $items;
        }
        
        if (is_user_logged_in()) {
            $current_user = wp_get_current_user();
            $items .= '<li class="menu-item menu-item-account"><a href="' . home_url('/my-account/my-recordings') . '">My Recordings</a></li>';
            $items .= '<li class="menu-item menu-item-logout"><a href="#" id="logout-link">Logout (' . esc_html($current_user->display_name) . ')</a></li>';
        } else {
            $items .= '<li class="menu-item menu-item-login"><a href="' . home_url('/login') . '">Login</a></li>';
            $items .= '<li class="menu-item menu-item-register"><a href="' . home_url('/register') . '">Register</a></li>';
        }
        
        return $items;
    }
    
    /**
     * Get login URL with redirect
     */
    public static function get_login_url($redirect_to = '') {
        $login_url = home_url('/login');
        
        if ($redirect_to) {
            $login_url = add_query_arg('redirect_to', urlencode($redirect_to), $login_url);
        }
        
        return $login_url;
    }
    
    /**
     * Get register URL with redirect
     */
    public static function get_register_url($redirect_to = '') {
        $register_url = home_url('/register');
        
        if ($redirect_to) {
            $register_url = add_query_arg('redirect_to', urlencode($redirect_to), $register_url);
        }
        
        return $register_url;
    }
}

// Initialize authentication system
VideoBookingAuth::init();
