<?php
/**
 * Registration Template
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header();
?>

<div class="video-booking-container">
    <div class="auth-container">
        <div class="auth-form-wrapper">
            <div class="auth-header">
                <h1>Create Your Account</h1>
                <p>Join our community and access exclusive workshop recordings</p>
            </div>
            
            <form id="register-form" class="auth-form">
                <?php wp_nonce_field('video_auth_nonce', 'nonce'); ?>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="first_name">First Name *</label>
                        <input type="text" id="first_name" name="first_name" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="last_name">Last Name *</label>
                        <input type="text" id="last_name" name="last_name" class="form-control" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="username">Username *</label>
                    <input type="text" id="username" name="username" class="form-control" required>
                    <small class="form-text">Choose a unique username (letters, numbers, and underscores only)</small>
                </div>
                
                <div class="form-group">
                    <label for="email">Email Address *</label>
                    <input type="email" id="email" name="email" class="form-control" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Password *</label>
                    <input type="password" id="password" name="password" class="form-control" required>
                    <small class="form-text">Minimum 6 characters</small>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password">Confirm Password *</label>
                    <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
                </div>
                
                <div class="form-group form-check">
                    <label>
                        <input type="checkbox" id="agree_terms" name="agree_terms" value="1" required>
                        I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
                    </label>
                </div>
                
                <div class="form-group form-check">
                    <label>
                        <input type="checkbox" id="newsletter" name="newsletter" value="1">
                        Subscribe to our newsletter for updates on new workshops
                    </label>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-full-width">Create Account</button>
                </div>
                
                <div class="auth-links">
                    <p>Already have an account? <a href="<?php echo home_url('/login'); ?>">Login here</a></p>
                </div>
            </form>
        </div>
        
        <div class="auth-benefits">
            <h3>What You'll Get</h3>
            <ul>
                <li><i class="fa fa-video-camera"></i> Instant access to purchased workshop recordings</li>
                <li><i class="fa fa-star"></i> Early bird discounts on new workshops</li>
                <li><i class="fa fa-mobile"></i> Watch on any device, anytime</li>
                <li><i class="fa fa-clock-o"></i> Extended access periods for all videos</li>
                <li><i class="fa fa-certificate"></i> Certificates of completion (where applicable)</li>
                <li><i class="fa fa-users"></i> Join our learning community</li>
            </ul>
            
            <div class="testimonial">
                <blockquote>
                    "The workshop recordings have been incredibly valuable for my personal growth. The quality is excellent and I can learn at my own pace."
                </blockquote>
                <cite>- Happy Student</cite>
            </div>
        </div>
    </div>
</div>

<style>
/* Registration specific styles */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 0;
}

.form-text {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    display: block;
}

.form-check a {
    color: #2c5aa0;
    text-decoration: none;
}

.form-check a:hover {
    text-decoration: underline;
}

/* Testimonial */
.testimonial {
    margin-top: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    border-left: 4px solid #ffc107;
}

.testimonial blockquote {
    font-style: italic;
    margin: 0 0 10px 0;
    font-size: 0.95em;
    line-height: 1.5;
}

.testimonial cite {
    font-size: 0.9em;
    opacity: 0.8;
}

/* Password strength indicator */
.password-strength {
    margin-top: 5px;
    height: 4px;
    background: #eee;
    border-radius: 2px;
    overflow: hidden;
}

.password-strength-bar {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.password-strength.weak .password-strength-bar {
    width: 33%;
    background: #dc3545;
}

.password-strength.medium .password-strength-bar {
    width: 66%;
    background: #ffc107;
}

.password-strength.strong .password-strength-bar {
    width: 100%;
    background: #28a745;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Password strength indicator
    $('#password').on('input', function() {
        const password = $(this).val();
        const $strengthIndicator = $('.password-strength');
        
        if (!$strengthIndicator.length) {
            $(this).after('<div class="password-strength"><div class="password-strength-bar"></div></div>');
        }
        
        let strength = 'weak';
        if (password.length >= 8 && /[A-Z]/.test(password) && /[0-9]/.test(password)) {
            strength = 'strong';
        } else if (password.length >= 6) {
            strength = 'medium';
        }
        
        $('.password-strength').removeClass('weak medium strong').addClass(strength);
    });
    
    // Username validation
    $('#username').on('input', function() {
        const username = $(this).val();
        const validUsername = /^[a-zA-Z0-9_]+$/.test(username);
        
        if (username && !validUsername) {
            $(this).addClass('error');
            if (!$(this).siblings('.form-error').length) {
                $(this).after('<span class="form-error">Username can only contain letters, numbers, and underscores</span>');
            }
        } else {
            $(this).removeClass('error');
            $(this).siblings('.form-error').remove();
        }
    });
    
    // Password confirmation
    $('#confirm_password').on('input', function() {
        const password = $('#password').val();
        const confirmPassword = $(this).val();
        
        if (confirmPassword && password !== confirmPassword) {
            $(this).addClass('error');
            if (!$(this).siblings('.form-error').length) {
                $(this).after('<span class="form-error">Passwords do not match</span>');
            }
        } else {
            $(this).removeClass('error');
            $(this).siblings('.form-error').remove();
        }
    });
    
    // Form submission
    $('#register-form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $submitButton = $form.find('button[type="submit"]');
        const originalText = $submitButton.text();
        
        // Clear previous errors
        $('.form-error').remove();
        $('.form-control').removeClass('error');
        $('.auth-error, .auth-success').remove();
        
        // Validate passwords match
        const password = $('#password').val();
        const confirmPassword = $('#confirm_password').val();
        
        if (password !== confirmPassword) {
            $('#confirm_password').addClass('error').after('<span class="form-error">Passwords do not match</span>');
            return;
        }
        
        // Check terms agreement
        if (!$('#agree_terms').is(':checked')) {
            $('#agree_terms').closest('.form-check').after('<div class="form-error">You must agree to the terms and conditions</div>');
            return;
        }
        
        // Add loading state
        $form.addClass('loading');
        $submitButton.text('Creating Account...');
        
        $.ajax({
            url: videoAuth.ajax_url,
            type: 'POST',
            data: $form.serialize() + '&action=video_register',
            success: function(response) {
                if (response.success) {
                    $form.prepend('<div class="auth-success">' + response.data.message + '</div>');
                    setTimeout(() => {
                        window.location.href = response.data.redirect_url;
                    }, 2000);
                } else {
                    $form.prepend('<div class="auth-error">' + response.data + '</div>');
                    $form.removeClass('loading');
                    $submitButton.text(originalText);
                }
            },
            error: function() {
                $form.prepend('<div class="auth-error">Network error. Please try again.</div>');
                $form.removeClass('loading');
                $submitButton.text(originalText);
            }
        });
    });
});
</script>

<?php get_footer(); ?>
