<?php
/**
 * Video Listing Template - Workshop Recordings Page
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header();

// Get all available videos
$videos = VideoBookingDB::get_videos(array('status' => 'available'));
?>

<div class="video-booking-container">
    <div class="workshop-recordings-header">
        <h1>Workshop Recordings</h1>
        <p>Access our comprehensive collection of workshop recordings. Purchase individual videos or take advantage of early bird pricing for upcoming workshops.</p>
    </div>
    
    <?php if (empty($videos)): ?>
        <div class="no-videos-message">
            <h2>No Videos Available</h2>
            <p>We're currently preparing our video collection. Please check back soon for exciting workshop recordings!</p>
        </div>
    <?php else: ?>
        <div class="video-grid">
            <?php foreach ($videos as $video): ?>
                <?php
                $video_slug = sanitize_title($video->title);
                $video_url = home_url('/workshop-recordings/' . $video_slug);
                $current_price = $video->is_early_bird ? $video->early_bird_price : $video->regular_price;
                $is_early_bird_active = $video->is_early_bird && $video->upload_status === 'pending';
                ?>
                <div class="video-card" data-video-id="<?php echo $video->id; ?>">
                    <div class="video-thumbnail">
                        <?php if ($video->thumbnail): ?>
                            <img src="<?php echo esc_url($video->thumbnail); ?>" alt="<?php echo esc_attr($video->title); ?>">
                        <?php else: ?>
                            <div class="no-thumbnail">
                                <i class="fa fa-play-circle"></i>
                                <span>Video Preview</span>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($is_early_bird_active): ?>
                            <div class="early-bird-badge">Early Bird</div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="video-info">
                        <h3 class="video-title">
                            <a href="<?php echo esc_url($video_url); ?>"><?php echo esc_html($video->title); ?></a>
                        </h3>
                        
                        <?php if ($video->description): ?>
                            <p class="video-description"><?php echo esc_html(wp_trim_words($video->description, 20)); ?></p>
                        <?php endif; ?>
                        
                        <div class="video-pricing">
                            <div class="price-info">
                                <span class="price-regular">₹<?php echo number_format($current_price, 2); ?></span>
                                <?php if ($video->is_early_bird && $video->regular_price > $video->early_bird_price): ?>
                                    <span class="price-original">₹<?php echo number_format($video->regular_price, 2); ?></span>
                                    <span class="savings">Save ₹<?php echo number_format($video->regular_price - $video->early_bird_price, 2); ?></span>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($is_early_bird_active && $video->early_bird_note): ?>
                                <div class="early-bird-note">
                                    <i class="fa fa-info-circle"></i>
                                    <?php echo esc_html($video->early_bird_note); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="video-actions">
                            <button class="btn btn-primary add-to-cart" 
                                    data-video-id="<?php echo $video->id; ?>" 
                                    data-early-bird="<?php echo $is_early_bird_active ? 1 : 0; ?>">
                                Add to Cart
                            </button>
                            <a href="<?php echo esc_url($video_url); ?>" class="btn btn-secondary">View Details</a>
                        </div>
                        
                        <div class="video-meta">
                            <span class="access-duration">
                                <i class="fa fa-clock-o"></i>
                                <?php echo $video->duration_days; ?> days access
                            </span>
                            <?php if ($is_early_bird_active): ?>
                                <span class="early-bird-status">
                                    <i class="fa fa-calendar"></i>
                                    Available after workshop
                                </span>
                            <?php else: ?>
                                <span class="instant-access">
                                    <i class="fa fa-bolt"></i>
                                    Instant access
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
    
    <!-- Cart Summary (if items in cart) -->
    <div id="cart-summary" class="cart-summary-widget" style="display: none;">
        <div class="cart-summary-content">
            <h4>Your Cart</h4>
            <div class="cart-items-count">
                <span class="cart-count">0</span> item(s)
            </div>
            <div class="cart-total">
                Total: <span class="cart-total-amount">₹0.00</span>
            </div>
            <div class="cart-actions">
                <a href="<?php echo home_url('/video-cart'); ?>" class="btn btn-primary btn-small">View Cart</a>
                <a href="<?php echo home_url('/video-checkout'); ?>" class="btn btn-success btn-small">Checkout</a>
            </div>
        </div>
    </div>
</div>

<style>
/* Workshop Recordings Specific Styles */
.workshop-recordings-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 20px;
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3f73 100%);
    color: white;
    border-radius: 12px;
}

.workshop-recordings-header h1 {
    font-size: 2.5em;
    margin-bottom: 15px;
    font-weight: bold;
}

.workshop-recordings-header p {
    font-size: 1.1em;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.no-videos-message {
    text-align: center;
    padding: 60px 20px;
    background: #f8f9fa;
    border-radius: 12px;
    margin: 40px 0;
}

.no-videos-message h2 {
    color: #666;
    margin-bottom: 15px;
}

.no-videos-message p {
    color: #888;
    font-size: 1.1em;
}

.no-thumbnail {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: #f0f0f0;
    color: #666;
}

.no-thumbnail i {
    font-size: 3em;
    margin-bottom: 10px;
    opacity: 0.5;
}

.price-original {
    text-decoration: line-through;
    color: #999;
    font-size: 0.9em;
    margin-left: 10px;
}

.savings {
    background: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8em;
    font-weight: bold;
    margin-left: 10px;
}

.video-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
    font-size: 0.9em;
    color: #666;
}

.video-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.instant-access {
    color: #28a745;
    font-weight: bold;
}

.early-bird-status {
    color: #ff6b35;
    font-weight: bold;
}

.video-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.video-actions .btn {
    flex: 1;
    text-align: center;
}

/* Cart Summary Widget */
.cart-summary-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    padding: 20px;
    min-width: 250px;
    z-index: 1000;
    border: 2px solid #2c5aa0;
}

.cart-summary-widget h4 {
    margin: 0 0 15px 0;
    color: #2c5aa0;
    font-size: 1.1em;
}

.cart-items-count {
    margin-bottom: 10px;
    color: #666;
}

.cart-total {
    font-weight: bold;
    color: #2c5aa0;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.cart-actions {
    display: flex;
    gap: 8px;
}

.cart-actions .btn-small {
    padding: 8px 12px;
    font-size: 0.9em;
    flex: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .workshop-recordings-header h1 {
        font-size: 2em;
    }
    
    .workshop-recordings-header p {
        font-size: 1em;
    }
    
    .video-actions {
        flex-direction: column;
    }
    
    .video-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .cart-summary-widget {
        bottom: 10px;
        right: 10px;
        left: 10px;
        min-width: auto;
    }
    
    .cart-actions {
        flex-direction: column;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Update cart summary on page load
    updateCartSummary();
    
    // Update cart summary when cart changes
    $(document).on('cart-updated', updateCartSummary);
    
    function updateCartSummary() {
        $.ajax({
            url: videoBooking.ajax_url,
            type: 'POST',
            data: {
                action: 'video_get_cart',
                nonce: videoBooking.nonce
            },
            success: function(response) {
                if (response.success && response.data.count > 0) {
                    $('.cart-count').text(response.data.count);
                    $('.cart-total-amount').text('₹' + response.data.total);
                    $('#cart-summary').show();
                } else {
                    $('#cart-summary').hide();
                }
            }
        });
    }
});
</script>

<?php get_footer(); ?>
