/**
 * Video Booking Admin JavaScript
 * 
 * @package VideoBooking
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Video Admin Object
    const VideoAdmin = {
        
        // Initialize
        init: function() {
            this.bindEvents();
            this.initMediaUploader();
        },
        
        // Bind events
        bindEvents: function() {
            // Save video form
            $(document).on('submit', '#video-form', this.saveVideo);
            
            // Delete video
            $(document).on('click', '.delete-video', this.deleteVideo);
            
            // Toggle video status
            $(document).on('click', '.toggle-status', this.toggleStatus);
            
            // Mark video as uploaded
            $(document).on('click', '.mark-uploaded', this.markUploaded);
            
            // Thumbnail upload
            $(document).on('click', '.upload-thumbnail', this.uploadThumbnail);
            $(document).on('click', '.remove-thumbnail', this.removeThumbnail);
            
            // Video upload
            $(document).on('click', '.upload-video', this.uploadVideo);
        },
        
        // Initialize media uploader
        initMediaUploader: function() {
            // Thumbnail uploader
            this.thumbnailUploader = wp.media({
                title: 'Choose Thumbnail Image',
                button: {
                    text: 'Use this image'
                },
                multiple: false,
                library: {
                    type: 'image'
                }
            });
            
            this.thumbnailUploader.on('select', function() {
                const attachment = VideoAdmin.thumbnailUploader.state().get('selection').first().toJSON();
                $('#thumbnail').val(attachment.url);
                $('.thumbnail-preview').html('<img src="' + attachment.url + '" alt="Thumbnail" style="max-width: 200px; height: auto;">');
                $('.remove-thumbnail').show();
            });
            
            // Video uploader
            this.videoUploader = wp.media({
                title: 'Choose Video File',
                button: {
                    text: 'Use this video'
                },
                multiple: false,
                library: {
                    type: 'video'
                }
            });
            
            this.videoUploader.on('select', function() {
                const attachment = VideoAdmin.videoUploader.state().get('selection').first().toJSON();
                $('#video_file').val(attachment.url);
                $('.video-preview').html('<p>Selected file: <strong>' + attachment.filename + '</strong></p>');
            });
        },
        
        // Save video
        saveVideo: function(e) {
            e.preventDefault();
            
            const $form = $(this);
            const $submitButton = $form.find('button[type="submit"]');
            const originalText = $submitButton.text();
            
            // Validate form
            if (!VideoAdmin.validateForm($form)) {
                return;
            }
            
            $submitButton.prop('disabled', true).text('Saving...');
            
            const formData = $form.serialize();
            formData += '&action=video_save';
            
            $.ajax({
                url: videoAdmin.ajax_url,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        VideoAdmin.showMessage('Video saved successfully!', 'success');
                        
                        // Redirect to videos list after 1 second
                        setTimeout(function() {
                            window.location.href = 'admin.php?page=video-booking';
                        }, 1000);
                    } else {
                        VideoAdmin.showMessage(response.data || 'Failed to save video', 'error');
                        $submitButton.prop('disabled', false).text(originalText);
                    }
                },
                error: function() {
                    VideoAdmin.showMessage('Network error. Please try again.', 'error');
                    $submitButton.prop('disabled', false).text(originalText);
                }
            });
        },
        
        // Delete video
        deleteVideo: function(e) {
            e.preventDefault();
            
            const $button = $(this);
            const videoId = $button.data('video-id');
            
            if (!confirm('Are you sure you want to delete this video? This action cannot be undone.')) {
                return;
            }
            
            $button.prop('disabled', true).text('Deleting...');
            
            $.ajax({
                url: videoAdmin.ajax_url,
                type: 'POST',
                data: {
                    action: 'video_delete',
                    nonce: videoAdmin.nonce,
                    video_id: videoId
                },
                success: function(response) {
                    if (response.success) {
                        $button.closest('tr').fadeOut(300, function() {
                            $(this).remove();
                        });
                        VideoAdmin.showMessage('Video deleted successfully', 'success');
                    } else {
                        VideoAdmin.showMessage(response.data || 'Failed to delete video', 'error');
                        $button.prop('disabled', false).text('Delete');
                    }
                },
                error: function() {
                    VideoAdmin.showMessage('Network error. Please try again.', 'error');
                    $button.prop('disabled', false).text('Delete');
                }
            });
        },
        
        // Toggle video status
        toggleStatus: function(e) {
            e.preventDefault();
            
            const $button = $(this);
            const videoId = $button.data('video-id');
            const $row = $button.closest('tr');
            
            $button.prop('disabled', true);
            
            $.ajax({
                url: videoAdmin.ajax_url,
                type: 'POST',
                data: {
                    action: 'video_toggle_status',
                    nonce: videoAdmin.nonce,
                    video_id: videoId
                },
                success: function(response) {
                    if (response.success) {
                        const newStatus = response.data.is_available;
                        const $statusBadge = $row.find('.status-badge');
                        
                        if (newStatus) {
                            $statusBadge.removeClass('status-inactive').addClass('status-active').text('Active');
                            $button.text('Deactivate');
                        } else {
                            $statusBadge.removeClass('status-active').addClass('status-inactive').text('Inactive');
                            $button.text('Activate');
                        }
                        
                        VideoAdmin.showMessage('Video status updated', 'success');
                    } else {
                        VideoAdmin.showMessage(response.data || 'Failed to update status', 'error');
                    }
                    
                    $button.prop('disabled', false);
                },
                error: function() {
                    VideoAdmin.showMessage('Network error. Please try again.', 'error');
                    $button.prop('disabled', false);
                }
            });
        },
        
        // Mark video as uploaded
        markUploaded: function(e) {
            e.preventDefault();
            
            const $button = $(this);
            const videoId = $button.data('video-id');
            const $row = $button.closest('tr');
            
            if (!confirm('Mark this video as uploaded? This will grant access to early bird customers and send notification emails.')) {
                return;
            }
            
            $button.prop('disabled', true).text('Processing...');
            
            $.ajax({
                url: videoAdmin.ajax_url,
                type: 'POST',
                data: {
                    action: 'video_mark_uploaded',
                    nonce: videoAdmin.nonce,
                    video_id: videoId
                },
                success: function(response) {
                    if (response.success) {
                        const $uploadStatus = $row.find('.upload-status');
                        $uploadStatus.removeClass('upload-pending').addClass('upload-uploaded').text('Uploaded');
                        $button.remove();
                        
                        VideoAdmin.showMessage('Video marked as uploaded. Early bird customers have been notified.', 'success');
                    } else {
                        VideoAdmin.showMessage(response.data || 'Failed to update upload status', 'error');
                        $button.prop('disabled', false).text('Mark as Uploaded');
                    }
                },
                error: function() {
                    VideoAdmin.showMessage('Network error. Please try again.', 'error');
                    $button.prop('disabled', false).text('Mark as Uploaded');
                }
            });
        },
        
        // Upload thumbnail
        uploadThumbnail: function(e) {
            e.preventDefault();
            VideoAdmin.thumbnailUploader.open();
        },
        
        // Remove thumbnail
        removeThumbnail: function(e) {
            e.preventDefault();
            $('#thumbnail').val('');
            $('.thumbnail-preview').empty();
            $(this).hide();
        },
        
        // Upload video
        uploadVideo: function(e) {
            e.preventDefault();
            VideoAdmin.videoUploader.open();
        },
        
        // Validate form
        validateForm: function($form) {
            let isValid = true;
            
            // Remove existing error messages
            $form.find('.error-message').remove();
            $form.find('.error').removeClass('error');
            
            // Required fields
            $form.find('[required]').each(function() {
                const $field = $(this);
                const value = $field.val().trim();
                
                if (!value) {
                    VideoAdmin.showFieldError($field, 'This field is required');
                    isValid = false;
                }
            });
            
            // Price validation
            const regularPrice = parseFloat($('#regular_price').val());
            const earlyBirdPrice = parseFloat($('#early_bird_price').val());
            const isEarlyBird = $('#is_early_bird').is(':checked');
            
            if (regularPrice < 0) {
                VideoAdmin.showFieldError($('#regular_price'), 'Price cannot be negative');
                isValid = false;
            }
            
            if (isEarlyBird && earlyBirdPrice >= regularPrice) {
                VideoAdmin.showFieldError($('#early_bird_price'), 'Early bird price must be less than regular price');
                isValid = false;
            }
            
            // Duration validation
            const duration = parseInt($('#duration_days').val());
            if (duration < 1) {
                VideoAdmin.showFieldError($('#duration_days'), 'Duration must be at least 1 day');
                isValid = false;
            }
            
            return isValid;
        },
        
        // Show field error
        showFieldError: function($field, message) {
            $field.addClass('error');
            const $error = $('<p class="error-message" style="color: #d63638; font-size: 13px; margin: 5px 0 0 0;">' + message + '</p>');
            $field.after($error);
        },
        
        // Show message
        showMessage: function(message, type) {
            const $notice = $('<div class="notice notice-' + type + ' is-dismissible"><p>' + message + '</p></div>');
            
            // Remove existing notices
            $('.notice').remove();
            
            // Add new notice
            $('.wrap h1').after($notice);
            
            // Auto-hide success messages
            if (type === 'success') {
                setTimeout(function() {
                    $notice.fadeOut();
                }, 3000);
            }
            
            // Scroll to top
            $('html, body').animate({ scrollTop: 0 }, 300);
        }
    };
    
    // Initialize Video Admin
    VideoAdmin.init();
    
    // Make VideoAdmin globally available
    window.VideoAdmin = VideoAdmin;
});
